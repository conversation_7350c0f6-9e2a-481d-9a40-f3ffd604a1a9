<!DOCTYPE html>
<html>
<head>
    <title>Simple ISIC Tree Test</title>
    <style>
        .level-section { color: red; font-weight: bold; }
        .level-division { color: orange; font-weight: bold; margin-left: 20px; }
        .level-group { color: green; margin-left: 40px; }
        .level-class { color: blue; margin-left: 60px; }
    </style>
</head>
<body>
    <h1>ISIC Rev.5 Hierarchy Test</h1>
    <div id="tree"></div>
    
    <script src="isic_rev5_data_corrected.js"></script>
    <script>
        console.log('Loaded data items:', isicData.length);
        
        // Build tree structure
        const treeData = {};
        isicData.forEach(item => {
            treeData[item.code] = { ...item, children: [] };
        });
        
        isicData.forEach(item => {
            if (item.parentCode && treeData[item.parentCode]) {
                treeData[item.parentCode].children.push(treeData[item.code]);
            }
        });
        
        // Find root nodes (sections)
        const rootNodes = isicData.filter(item => !item.parentCode);
        console.log('Root nodes found:', rootNodes.length);
        rootNodes.forEach(node => console.log('Root:', node.code, node.level, node.name));
        
        // Render tree (focusing on C section)
        function renderNode(node, container, depth = 0) {
            const div = document.createElement('div');
            div.className = `level-${node.level.toLowerCase()}`;
            div.textContent = `[${'  '.repeat(depth)}${node.code}] ${node.level}: ${node.name}`;
            container.appendChild(div);
            
            // Only show first few children to avoid overwhelming display
            const childrenToShow = node.code === 'C' ? node.children.slice(0, 3) : 
                                   (depth < 2 ? node.children.slice(0, 2) : []);
            
            childrenToShow.forEach(child => {
                renderNode(child, container, depth + 1);
            });
            
            if (node.children.length > childrenToShow.length) {
                const moreDiv = document.createElement('div');
                moreDiv.textContent = `${'  '.repeat(depth + 1)}... and ${node.children.length - childrenToShow.length} more`;
                moreDiv.style.color = 'gray';
                moreDiv.style.fontStyle = 'italic';
                container.appendChild(moreDiv);
            }
        }
        
        const treeContainer = document.getElementById('tree');
        
        // Focus on C section
        const cSection = treeData['C'];
        if (cSection) {
            console.log('C Section found:', cSection.level, cSection.name);
            console.log('C Section children:', cSection.children.length);
            cSection.children.slice(0, 3).forEach(child => {
                console.log('C child:', child.code, child.level, child.name);
            });
            
            renderNode(cSection, treeContainer);
        } else {
            treeContainer.innerHTML = '<p style="color: red;">C Section not found!</p>';
        }
        
        // Also show A section for comparison
        const aSection = treeData['A'];
        if (aSection) {
            const hr = document.createElement('hr');
            treeContainer.appendChild(hr);
            renderNode(aSection, treeContainer);
        }
    </script>
</body>
</html>