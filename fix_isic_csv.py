import csv

print('=== Creating Corrected ISIC Rev.5 CSV ===')

# Read original data and fix structural issues
corrected_data = []
issues_fixed = 0

with open('/mnt/d/Research/start-up/Classification/final_data/ISIC_Rev5/ISIC_Rev5_converted.csv', 'r', encoding='utf-8-sig') as f:
    reader = csv.DictReader(f)
    
    for row in reader:
        level = row['Classification Level'].strip()
        code = row['Classification Code'].strip()
        parent = row['Parent Code'].strip()
        
        # Fix parent codes based on ISIC structure rules
        original_parent = parent
        
        if level == 'Section':
            # Sections have no parent
            parent = ''
            
        elif level == 'Division':
            # Divisions should reference section (first letter)
            if code:
                parent = code[0]
                
        elif level == 'Group':
            # Groups should reference division (first 3 characters)
            if len(code) >= 3:
                parent = code[:3]
                
        elif level == 'Class':
            # Classes should reference group (first 4 characters)  
            if len(code) >= 4:
                parent = code[:4]
        
        if original_parent != parent:
            issues_fixed += 1
        
        # Create corrected row
        corrected_row = {
            'Classification Level': level,
            'Classification Code': code,
            'Classification Name': row['Classification Name'],
            'Parent Code': parent,
            'Core Keywords': row['Core Keywords'],
            'Description': row['Description']
        }
        
        corrected_data.append(corrected_row)

# Write corrected CSV
output_file = '/mnt/d/Research/start-up/Classification/final_data/ISIC_Rev5/ISIC_Rev5_converted_CORRECTED.csv'
with open(output_file, 'w', encoding='utf-8', newline='') as f:
    fieldnames = ['Classification Level', 'Classification Code', 'Classification Name', 'Parent Code', 'Core Keywords', 'Description']
    writer = csv.DictWriter(f, fieldnames=fieldnames)
    
    writer.writeheader()
    writer.writerows(corrected_data)

print(f'Fixed {issues_fixed} structural issues')
print(f'Created corrected file: ISIC_Rev5_converted_CORRECTED.csv')
print(f'Total entries: {len(corrected_data)}')

# Show some examples of corrections
print()
print('=== CORRECTION EXAMPLES ===')
examples = [
    ('A', 'Section'), ('A01', 'Division'), ('A011', 'Group'), ('A0111', 'Class'),
    ('C', 'Section'), ('C10', 'Division'), ('C101', 'Group'), ('C1011', 'Class')
]

for code, expected_level in examples:
    for row in corrected_data:
        if row['Classification Code'] == code and row['Classification Level'] == expected_level:
            print(f'{code} ({expected_level}) -> parent: "{row["Parent Code"]}"')
            break