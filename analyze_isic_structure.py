import csv

# Analyze the original CSV structure
print('=== ISIC Rev.5 Original Data Analysis ===')
print()

with open('/mnt/d/Research/start-up/Classification/final_data/ISIC_Rev5/ISIC_Rev5_converted.csv', 'r', encoding='utf-8-sig') as f:
    reader = csv.DictReader(f)
    
    sections = []
    divisions = []
    groups = []
    classes = []
    issues = []
    
    for i, row in enumerate(reader, 1):
        level = row['Classification Level'].strip()
        code = row['Classification Code'].strip()
        parent = row['Parent Code'].strip()
        
        if level == 'Section':
            sections.append((code, parent))
            if parent != '':
                issues.append(f'Line {i}: Section {code} has parent "{parent}" (should be empty)')
                
        elif level == 'Division':
            divisions.append((code, parent))
            # Division should reference section (single letter)
            expected_parent = code[0] if code else ''
            if parent != expected_parent:
                issues.append(f'Line {i}: Division {code} has parent "{parent}" (should be "{expected_parent}")')
                
        elif level == 'Group':
            groups.append((code, parent))
            # Group should reference division (first 2-3 chars)
            if len(code) >= 3:
                expected_parent = code[:3]  # e.g., A011 -> A01
                if parent != expected_parent:
                    issues.append(f'Line {i}: Group {code} has parent "{parent}" (should be "{expected_parent}")')
                    
        elif level == 'Class':
            classes.append((code, parent))
            # Class should reference group (first 4 chars)
            if len(code) >= 4:
                expected_parent = code[:4]  # e.g., A0111 -> A011
                if parent != expected_parent:
                    issues.append(f'Line {i}: Class {code} has parent "{parent}" (should be "{expected_parent}")')

print(f'Found {len(sections)} Sections, {len(divisions)} Divisions, {len(groups)} Groups, {len(classes)} Classes')
print()

if issues:
    print(f'=== FOUND {len(issues)} STRUCTURAL ISSUES ===')
    for issue in issues[:20]:  # Show first 20 issues
        print(issue)
    if len(issues) > 20:
        print(f'... and {len(issues) - 20} more issues')
else:
    print('=== NO STRUCTURAL ISSUES FOUND ===')
    print('Original data structure appears to be correct')

print()
print('=== SAMPLE ENTRIES ===')
print('A section entries:')
for code, parent in [(c, p) for c, p in sections if c.startswith('A')][:3]:
    print(f'  {code} -> parent: "{parent}"')
    
print('A division entries:')  
for code, parent in [(c, p) for c, p in divisions if c.startswith('A')][:3]:
    print(f'  {code} -> parent: "{parent}"')
    
print('C section entries:')
for code, parent in [(c, p) for c, p in sections if c.startswith('C')][:3]:
    print(f'  {code} -> parent: "{parent}"')
    
print('C division entries:')
for code, parent in [(c, p) for c, p in divisions if c.startswith('C')][:3]:
    print(f'  {code} -> parent: "{parent}"')